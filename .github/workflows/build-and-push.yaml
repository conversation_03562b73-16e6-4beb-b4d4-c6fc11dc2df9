name: Build and Push

on:
  push:
    paths:
      - '!charts/**.md'
    branches:
      - main
    tags:
      - v*

env:
  registry: ghcr.io
  image-name: ${{ github.repository }}

jobs:
  build-and-push:
    runs-on: ubuntu-latest

    permissions:
      contents: read
      packages: write

    steps:
      - name: Checkout Repository
        uses: actions/checkout@v4

      - name: Set up QEMU
        uses: docker/setup-qemu-action@v3

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Determine tags
        id: metadata
        uses: docker/metadata-action@v5
        with:
          images: ${{ env.registry }}/${{ env.image-name }}
          tags: |
            type=sha,prefix=,format=long
            type=schedule
            type=ref,event=tag
            type=ref,event=branch
            type=ref,event=pr
            type=raw,value=latest,enable={{ is_default_branch }}

      - name: Log in to the Container registry
        uses: docker/login-action@v3
        with:
          registry: ${{ env.registry }}
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Build and push ${{ env.image-name }}
        uses: docker/build-push-action@v6
        with:
          context: .
          platforms: linux/amd64,linux/arm64
          provenance: false
          push: true
          tags: ${{ steps.metadata.outputs.tags }}
          labels: ${{ steps.metadata.outputs.labels }}
          cache-from: type=gha
          cache-to: type=gha,mode=max

concurrency:
  group: ${{ github.ref }}
  cancel-in-progress: true
