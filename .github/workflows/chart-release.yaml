name: Release Charts

on:
  push:
    paths:
      - charts/**
      - '!**.md'
    branches:
      - main

jobs:
  release:
    runs-on: ubuntu-latest

    permissions:
      contents: write

    steps:
      - name: Checkout
        uses: actions/checkout@8ade135a41bc03ea155e62e844d188df1ea18608 # v4.1.0
        with:
          fetch-depth: 0

      - name: Configure Git
        run: |
          git config user.name "$GITHUB_ACTOR"
          git config user.email "$<EMAIL>"

      - name: Install Helm
        uses: azure/setup-helm@5119fcb9089d432beecbf79bb2c7915207344b78 # v3.5
        with:
          version: v3.13.0

      - name: Run chart-releaser
        uses: helm/chart-releaser-action@a917fd15b20e8b64b94d9158ad54cd6345335584 # v1.6.0
        with:
          charts_dir: charts
          config: cr.yaml
        env:
          CR_TOKEN: "${{ secrets.GITHUB_TOKEN }}"
