name: '[CI/CD] Update README metadata'

on:
  pull_request_target:
    branches:
      - main
    paths:
      - 'charts/*/values.yaml'
# Remove all permissions by default
permissions: {}
jobs:
  update-readme-metadata:
    runs-on: ubuntu-latest
    permissions:
      contents: write
    steps:
      - name: Install readme-generator-for-helm
        run: npm install -g @bitnami/readme-generator-for-helm
      - name: Checkout
        uses: actions/checkout@8ade135a41bc03ea155e62e844d188df1ea18608
        with:
          path: charts
          ref: ${{github.event.pull_request.head.ref}}
          repository: ${{github.event.pull_request.head.repo.full_name}}
          token: ${{ secrets.GITHUB_TOKEN }}
      - name: Execute readme-generator-for-helm
        env:
          DIFF_URL: "${{github.event.pull_request.diff_url}}"
          TEMP_FILE: "${{runner.temp}}/pr-${{github.event.number}}.diff"
        run: |
          # This request doesn't consume API calls.
          curl -Lkso $TEMP_FILE $DIFF_URL
          files_changed="$(sed -nr 's/[\-\+]{3} [ab]\/(.*)/\1/p' $TEMP_FILE | sort | uniq)"
          # Adding || true to avoid "Process exited with code 1" errors
          charts_dirs_changed="$(echo "$files_changed" | xargs dirname | grep -o "pgcat/[^/]*" | sort | uniq || true)"
          for chart in ${charts_dirs_changed}; do
            echo "Updating README.md for ${chart}"
            readme-generator --values "charts/${chart}/values.yaml" --readme "charts/${chart}/README.md" --schema "/tmp/schema.json"
          done
      - name: Push changes
        run: |
          # Push all the changes
          cd charts
          if git status -s | grep pgcat; then
            git config user.name "$GITHUB_ACTOR"
            git config user.email "$<EMAIL>"
            git add . && git commit -am "Update README.md with readme-generator-for-helm" --signoff && git push
          fi
