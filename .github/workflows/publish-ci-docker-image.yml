name: publish-ci-docker-image
on:
  push:
    branches: [ main ]
jobs:
  publish-ci-docker-image:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Login to GitHub Container Registry
        uses: docker/login-action@v1
        with:
          registry: ghcr.io
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}
      - name: Build CI Docker image
        run: |
          docker build . -f Dockerfile.ci --tag ghcr.io/postgresml/pgcat-ci:latest
          docker run ghcr.io/postgresml/pgcat-ci:latest
          docker push ghcr.io/postgresml/pgcat-ci:latest
