name: pgcat package (deb)

on:
  push:
    tags:
      - v*
  workflow_dispatch:
    inputs:
      packageVersion:
        default: "1.1.2-dev1"
jobs:
  build:
    strategy:
      max-parallel: 1
      fail-fast: false # Let the other job finish, or they can lock each other out
      matrix:
        os: ["buildjet-4vcpu-ubuntu-2204", "buildjet-4vcpu-ubuntu-2204-arm"]

    runs-on: ${{ matrix.os }}
    steps:
    - uses: actions/checkout@v3
    - name: Set package version
      if: github.event_name == 'push'  # For push event
      run: |
        TAG=${{ github.ref_name }}
        echo "packageVersion=${TAG#v}" >> "$GITHUB_ENV"
    - name: Set package version (manual dispatch)
      if: github.event_name == 'workflow_dispatch'  # For manual dispatch
      run: echo "packageVersion=${{ github.event.inputs.packageVersion }}" >> "$GITHUB_ENV"
    - uses: actions-rs/toolchain@v1
      with:
        toolchain: stable
    - name: Install dependencies
      env:
        DEBIAN_FRONTEND: noninteractive
        TZ: Etc/UTC
      run: |
        curl -sLO https://github.com/deb-s3/deb-s3/releases/download/0.11.4/deb-s3-0.11.4.gem
        sudo gem install deb-s3-0.11.4.gem
        dpkg-deb --version
    - name: Build and release package
      env:
        AWS_ACCESS_KEY_ID: ${{ vars.AWS_ACCESS_KEY_ID }}
        AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
        AWS_DEFAULT_REGION: ${{ vars.AWS_DEFAULT_REGION }}
      run: |
        if [[ $(arch) == "x86_64" ]]; then
          export ARCH=amd64
        else
          export ARCH=arm64
        fi

        bash utilities/deb.sh ${{ env.packageVersion }}

        deb-s3 upload \
          --lock \
          --bucket apt.postgresml.org \
          pgcat-${{ env.packageVersion }}-ubuntu22.04-${ARCH}.deb \
          --codename $(lsb_release -cs)
