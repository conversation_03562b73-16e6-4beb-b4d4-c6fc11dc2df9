name: Lint and Test Charts

on:
  pull_request:
    paths:
      - charts/**
      - '!charts/**.md'
jobs:
  lint-test:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v3.1.0
        with:
          fetch-depth: 0

      - name: Set up Helm
        uses: azure/setup-helm@v3
        with:
          version: v3.8.1

      # Python is required because `ct lint` runs Yamale (https://github.com/23andMe/Yamale) and
      # yamllint (https://github.com/adrienverge/yamllint) which require Python
      - name: Set up Python
        uses: actions/setup-python@v5.1.0
        with:
          python-version: 3.7

      - name: Set up chart-testing
        uses: helm/chart-testing-action@v2.2.1
        with:
          version: v3.5.1

      - name: Run chart-testing (list-changed)
        id: list-changed
        run: |
          changed=$(ct list-changed --config ct.yaml)
          if [[ -n "$changed" ]]; then
            echo "changed=true" >> $GITHUB_OUTPUT
          fi

      - name: Run chart-testing (lint)
        run: ct lint --config ct.yaml

      - name: Create kind cluster
        uses: helm/kind-action@v1.10.0
        if: steps.list-changed.outputs.changed == 'true'

      - name: Run chart-testing (install)
        run: ct install --config ct.yaml
