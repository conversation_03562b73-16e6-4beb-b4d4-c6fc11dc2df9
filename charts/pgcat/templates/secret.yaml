apiVersion: v1
kind: Secret
metadata:
  name: {{ include "pgcat.fullname" . }}
  labels:
    {{- include "pgcat.labels" . | nindent 4 }}
type: Opaque
stringData:
  pgcat.toml: |
    [general]
    host = {{ .Values.configuration.general.host | quote }}
    port = {{ .Values.configuration.general.port }}
    enable_prometheus_exporter = {{ .Values.configuration.general.enable_prometheus_exporter }}
    prometheus_exporter_port = {{ .Values.configuration.general.prometheus_exporter_port }}
    connect_timeout = {{ .Values.configuration.general.connect_timeout }}
    idle_timeout = {{ .Values.configuration.general.idle_timeout | int }}
    server_lifetime = {{ .Values.configuration.general.server_lifetime | int }}
    server_tls = {{ .Values.configuration.general.server_tls }}
    idle_client_in_transaction_timeout = {{ .Values.configuration.general.idle_client_in_transaction_timeout | int }}
    healthcheck_timeout = {{ .Values.configuration.general.healthcheck_timeout }}
    healthcheck_delay = {{ .Values.configuration.general.healthcheck_delay }}
    shutdown_timeout = {{ .Values.configuration.general.shutdown_timeout }}
    ban_time = {{ .Values.configuration.general.ban_time }}
    log_client_connections = {{ .Values.configuration.general.log_client_connections }}
    log_client_disconnections = {{ .Values.configuration.general.log_client_disconnections }}
    tcp_keepalives_idle = {{ .Values.configuration.general.tcp_keepalives_idle }}
    tcp_keepalives_count = {{ .Values.configuration.general.tcp_keepalives_count }}
    tcp_keepalives_interval = {{ .Values.configuration.general.tcp_keepalives_interval }}
    {{- if and (ne .Values.configuration.general.tls_certificate "-") (ne .Values.configuration.general.tls_private_key "-") }}
    tls_certificate = "{{ .Values.configuration.general.tls_certificate }}"
    tls_private_key = "{{ .Values.configuration.general.tls_private_key }}"
    {{- end }}
    admin_username = {{ .Values.configuration.general.admin_username | quote }}
    admin_password = {{ .Values.configuration.general.admin_password | quote }}
    {{- if and .Values.configuration.general.auth_query_user .Values.configuration.general.auth_query_password .Values.configuration.general.auth_query }}
    auth_query = {{ .Values.configuration.general.auth_query | quote }}
    auth_query_user = {{ .Values.configuration.general.auth_query_user | quote }}
    auth_query_password = {{ .Values.configuration.general.auth_query_password | quote }}
    {{- end }}

    {{- range $pool := .Values.configuration.pools }}

    ##
    ## pool for {{ $pool.name }}
    ##
    [pools.{{ $pool.name | quote }}]
    pool_mode = {{ default "transaction" $pool.pool_mode | quote }}
    load_balancing_mode = {{ default "random" $pool.load_balancing_mode | quote }}
    default_role = {{ default "any" $pool.default_role | quote }}
    prepared_statements_cache_size = {{ default 500 $pool.prepared_statements_cache_size }}
    query_parser_enabled = {{ default true $pool.query_parser_enabled }}
    query_parser_read_write_splitting = {{ default true $pool.query_parser_read_write_splitting }}
    primary_reads_enabled = {{ default true $pool.primary_reads_enabled }}
    db_activity_based_routing = {{ default false $pool.db_activity_based_routing }}
    db_activity_based_ms_init_delay = {{ default 100 $pool.db_activity_based_ms_init_delay }}
    db_activity_ttl = {{ default 900 $pool.db_activity_ttl }}
    table_mutation_cache_ttl = {{ default 50 $pool.table_mutation_cache_ttl }}
    sharding_function = {{ default "pg_bigint_hash" $pool.sharding_function | quote }}

    {{-   range $index, $user := $pool.users }}

    ## pool {{ $pool.name }} user {{ $user.username | quote }}
    ##
    [pools.{{ $pool.name | quote }}.users.{{ $index }}]
    username = {{ $user.username | quote }}
    {{- if $user.password }}
    password = {{ $user.password | quote }}
    {{- else if and $user.passwordSecret.name $user.passwordSecret.key }}
    {{- $secret := (lookup "v1" "Secret" $.Release.Namespace $user.passwordSecret.name) }}
    {{- if $secret }}
    {{- $password := index $secret.data $user.passwordSecret.key | b64dec }}
    password = {{ $password | quote }}
    {{- end }}
    {{- end }}
    pool_size = {{ $user.pool_size }}
    statement_timeout = {{ default 0 $user.statement_timeout }}
    min_pool_size = {{ default 3 $user.min_pool_size }}
    {{- if $user.server_lifetime }}
    server_lifetime = {{ $user.server_lifetime }}
    {{- end }}
    {{-     if and $user.server_username $user.server_password }}
    server_username = {{ $user.server_username | quote }}
    server_password = {{ $user.server_password | quote }}
    {{-     end }}
    {{-   end }}

    {{-   range $index, $shard := $pool.shards }}

    ## pool {{ $pool.name }} database {{ $shard.database }}
    ##
    [pools.{{ $pool.name | quote }}.shards.{{ $index }}]
    {{-     if gt (len $shard.servers) 0}}
    servers = [
    {{-       range  $server := $shard.servers }}
        [ {{ $server.host | quote }}, {{ $server.port }}, {{ $server.role | quote }} ],
    {{-       end }}
    ]
    {{-     end }}
    database = {{ $shard.database | quote }}
    {{-   end }}
    {{- end }}
