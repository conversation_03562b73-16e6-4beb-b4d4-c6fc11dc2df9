{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "grafana", "uid": "-- <PERSON><PERSON> --"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "description": "A dashboard to monitor PgCat deployments. It is based on the metrics exported by the Prometheus exporter that comes bundled with PgCat\n\nPlease visit https://github.com/postgresml/pgcat for more information ", "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 1, "id": 4, "links": [], "panels": [{"gridPos": {"h": 1, "w": 24, "x": 0, "y": 0}, "id": 39, "title": "Throughput Metrics", "type": "row"}, {"datasource": {"type": "prometheus", "uid": "fdwe81suzec5ce"}, "description": "For this metric, each individual query outside a transaction is counted as one transaction. All queries that run inside a transaction are counted as one transaction.", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 100, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "smooth", "lineWidth": 1, "pointSize": 3, "scaleDistribution": {"type": "linear"}, "showPoints": "always", "spanNulls": false, "stacking": {"group": "A", "mode": "normal"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 6, "w": 6, "x": 0, "y": 1}, "id": 4, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": false}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "fdwe81suzec5ce"}, "disableTextWrap": false, "editorMode": "builder", "exemplar": false, "expr": "increase(pgcat_servers_transaction_count{pool=~\"$pool\", role=~\"$role\", shard=~\"$shard_id\", index=~\"$instance_index\", host=~\"$host\", user=~\"$user\"}[1m])", "fullMetaSearch": false, "includeNullMetadata": true, "instant": false, "legendFormat": "host:{{host}}, identifier:{{role}}{{index}}, shard_id:{{shard}}, pool:{{pool}}", "range": true, "refId": "A", "useBackend": false}], "title": "Transaction Count", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "fdwe81suzec5ce"}, "description": "The number of individual queries run", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 100, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "smooth", "lineWidth": 1, "pointSize": 3, "scaleDistribution": {"type": "linear"}, "showPoints": "always", "spanNulls": false, "stacking": {"group": "A", "mode": "normal"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 6, "w": 6, "x": 6, "y": 1}, "id": 41, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": false}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "fdwe81suzec5ce"}, "disableTextWrap": false, "editorMode": "builder", "exemplar": false, "expr": "increase(pgcat_servers_query_count{pool=~\"$pool\", role=~\"$role\", shard=~\"$shard_id\", index=~\"$instance_index\", host=~\"$host\", user=~\"$user\"}[1m])", "fullMetaSearch": false, "includeNullMetadata": true, "instant": false, "legendFormat": "host:{{host}}, identifier:{{role}}{{index}}, shard_id:{{shard}}, pool:{{pool}}", "range": true, "refId": "A", "useBackend": false}], "title": "Query Count", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "fdwe81suzec5ce"}, "description": "Average Query Latency", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "smooth", "lineWidth": 1, "pointSize": 2, "scaleDistribution": {"type": "linear"}, "showPoints": "always", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "ms"}, "overrides": []}, "gridPos": {"h": 6, "w": 6, "x": 12, "y": 1}, "id": 38, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": false}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "fdwe81suzec5ce"}, "disableTextWrap": false, "editorMode": "builder", "exemplar": false, "expr": "pgcat_stats_avg_query_time{pool=~\"$pool\", role=~\"$role\", shard=~\"$shard_id\", index=~\"$instance_index\", host=~\"$host\", user=~\"$user\"}", "fullMetaSearch": false, "includeNullMetadata": true, "instant": false, "legendFormat": "host:{{host}}, identifier:{{role}}{{index}}, shard_id:{{shard}}, pool:{{pool}}", "range": true, "refId": "A", "useBackend": false}], "title": "Average Query Latency", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "fdwe81suzec5ce"}, "description": "Average latency of transactions", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "smooth", "lineWidth": 1, "pointSize": 2, "scaleDistribution": {"type": "linear"}, "showPoints": "always", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "ms"}, "overrides": []}, "gridPos": {"h": 6, "w": 6, "x": 18, "y": 1}, "id": 42, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": false}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "fdwe81suzec5ce"}, "disableTextWrap": false, "editorMode": "builder", "exemplar": false, "expr": "pgcat_stats_avg_xact_time{pool=~\"$pool\", role=~\"$role\", shard=~\"$shard_id\", index=~\"$instance_index\", host=~\"$host\", user=~\"$user\"}", "fullMetaSearch": false, "includeNullMetadata": true, "instant": false, "legendFormat": "host:{{host}}, identifier:{{role}}{{index}}, shard_id:{{shard}}, pool:{{pool}}", "range": true, "refId": "A", "useBackend": false}], "title": "Average Transaction Latency", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "fdwe81suzec5ce"}, "description": "Data received in bytes", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 100, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "smooth", "lineWidth": 1, "pointSize": 3, "scaleDistribution": {"type": "linear"}, "showPoints": "always", "spanNulls": false, "stacking": {"group": "A", "mode": "normal"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "decbytes"}, "overrides": []}, "gridPos": {"h": 6, "w": 12, "x": 0, "y": 7}, "id": 40, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": false}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "fdwe81suzec5ce"}, "disableTextWrap": false, "editorMode": "builder", "exemplar": false, "expr": "increase(pgcat_servers_bytes_received{pool=~\"$pool\", role=~\"$role\", shard=~\"$shard_id\", index=~\"$instance_index\", host=~\"$host\", user=~\"$user\"}[1m])", "fullMetaSearch": false, "includeNullMetadata": true, "instant": false, "legendFormat": "host:{{host}}, identifier:{{role}}{{index}}, shard_id:{{shard}}, pool:{{pool}}", "range": true, "refId": "A", "useBackend": false}], "title": "Data received", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "fdwe81suzec5ce"}, "description": "Data sent in bytes", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 100, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "smooth", "lineWidth": 1, "pointSize": 3, "scaleDistribution": {"type": "linear"}, "showPoints": "always", "spanNulls": false, "stacking": {"group": "A", "mode": "normal"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "decbytes"}, "overrides": []}, "gridPos": {"h": 6, "w": 12, "x": 12, "y": 7}, "id": 10, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": false}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "fdwe81suzec5ce"}, "disableTextWrap": false, "editorMode": "builder", "exemplar": false, "expr": "increase(pgcat_servers_bytes_sent{pool=~\"$pool\", role=~\"$role\", shard=~\"$shard_id\", index=~\"$instance_index\", host=~\"$host\", user=~\"$user\"}[1m])", "fullMetaSearch": false, "includeNullMetadata": true, "instant": false, "legendFormat": "host:{{host}}, identifier:{{role}}{{index}}, shard_id:{{shard}}, pool:{{pool}}", "range": true, "refId": "A", "useBackend": false}], "title": "Data Sent", "type": "timeseries"}, {"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 13}, "id": 22, "panels": [], "title": "Capacity metrics", "type": "row"}, {"datasource": {"type": "prometheus", "uid": "fdwe81suzec5ce"}, "description": "This is the ratio between the number of active server connections to the pool size. Persistently high ratio (e.g. 80%-100%) may suggest the need for a larger pool or a more performant Database", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "smooth", "lineWidth": 1, "pointSize": 3, "scaleDistribution": {"type": "linear"}, "showPoints": "always", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "area"}}, "mappings": [], "max": 150, "min": 0, "thresholds": {"mode": "percentage", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 60}]}, "unit": "percent"}, "overrides": []}, "gridPos": {"h": 8, "w": 13, "x": 0, "y": 14}, "id": 30, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": false}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "fdwe81suzec5ce"}, "disableTextWrap": false, "editorMode": "code", "exemplar": false, "expr": "(pgcat_servers_active_count{pool=~\"$pool\", role=~\"$role\", shard=~\"$shard_id\", index=~\"$instance_index\", host=~\"$host\", user=~\"$user\"} / pgcat_databases_pool_size{pool=~\"$pool\", role=~\"$role\", shard=~\"$shard_id\", index=~\"$instance_index\", host=~\"$host\", user=~\"$user\"}) * 100  ", "fullMetaSearch": false, "includeNullMetadata": true, "instant": false, "legendFormat": "host:{{host}}, identifier:{{role}}{{index}}, shard_id:{{shard}}, pool:{{pool}}, user:{{user}}", "range": true, "refId": "A", "useBackend": false}], "title": "Percentage Server Pool Utilization", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "fdwe81suzec5ce"}, "description": "The number of Clients currently waiting in the queue waiting for a server connection to be assigned. This should remain close to 0. Any persistent deviation from zero means clients are blocked from querying the database", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "bars", "fillOpacity": 100, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "smooth", "lineStyle": {"fill": "solid"}, "lineWidth": 1, "pointSize": 2, "scaleDistribution": {"type": "linear"}, "showPoints": "always", "spanNulls": false, "stacking": {"group": "A", "mode": "normal"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 11, "x": 13, "y": 14}, "id": 31, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "fdwe81suzec5ce"}, "disableTextWrap": false, "editorMode": "builder", "exemplar": false, "expr": "pgcat_pools_cl_waiting{pool=~\"$pool\", user=~\"$user\"}", "fullMetaSearch": false, "includeNullMetadata": true, "instant": false, "legendFormat": "Pool:{{pool}}, User:{{user}}", "range": true, "refId": "A", "useBackend": false}], "title": "Waiting Clients", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "fdwe81suzec5ce"}, "description": "Banned connections won't get queries routed to them until they are unbanned. Instances are unbanned after the ban timer expires or if all replicas in the pool are banned so they are all unbanned as a failsafe. Primary instances are never banned", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "bars", "fillOpacity": 50, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "smooth", "lineWidth": 1, "pointSize": 2, "scaleDistribution": {"type": "linear"}, "showPoints": "always", "spanNulls": false, "stacking": {"group": "A", "mode": "normal"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 7, "w": 8, "x": 0, "y": 22}, "id": 34, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": false}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "fdwe81suzec5ce"}, "disableTextWrap": false, "editorMode": "builder", "exemplar": false, "expr": "pgcat_servers_is_banned{pool=~\"$pool\", role=~\"$role\", shard=~\"$shard_id\", index=~\"$instance_index\", host=~\"$host\", user=~\"$user\"}", "fullMetaSearch": false, "includeNullMetadata": true, "instant": false, "legendFormat": "host:{{host}}, identifier:{{role}}{{index}}, shard_id:{{shard}}, pool:{{pool}}, user:{{user}}", "range": true, "refId": "A", "useBackend": false}], "title": "Banned Connections", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "fdwe81suzec5ce"}, "description": "Paused connection are connections that belong to pool that was paused by the administrator", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "bars", "fillOpacity": 50, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "smooth", "lineWidth": 1, "pointSize": 2, "scaleDistribution": {"type": "linear"}, "showPoints": "always", "spanNulls": false, "stacking": {"group": "A", "mode": "normal"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 7, "w": 8, "x": 8, "y": 22}, "id": 35, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": false}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "fdwe81suzec5ce"}, "disableTextWrap": false, "editorMode": "builder", "exemplar": false, "expr": "pgcat_servers_is_paused{pool=~\"$pool\", role=~\"$role\", shard=~\"$shard_id\", index=~\"$instance_index\", host=~\"$host\", user=~\"$user\"}", "fullMetaSearch": false, "includeNullMetadata": true, "instant": false, "legendFormat": "host:{{host}}, identifier:{{role}}{{index}}, shard_id:{{shard}}, pool:{{pool}}, user:{{user}}", "range": true, "refId": "A", "useBackend": false}], "title": "Paused Connections", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "fdwe81suzec5ce"}, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "smooth", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "µs"}, "overrides": []}, "gridPos": {"h": 7, "w": 8, "x": 16, "y": 22}, "id": 44, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": false}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "fdwe81suzec5ce"}, "disableTextWrap": false, "editorMode": "builder", "exemplar": false, "expr": "(pgcat_pools_maxwait{pool=~\"$pool\", user=~\"$user\"} * 1000 * 1000) + pgcat_pools_maxwait_us{pool=~\"$pool\", user=~\"$user\"}", "fullMetaSearch": false, "includeNullMetadata": true, "instant": false, "legendFormat": "host:{{host}}, identifier:{{role}}{{index}}, shard_id:{{shard}}, pool:{{pool}}", "range": true, "refId": "A", "useBackend": false}], "title": "Maximum wait time by Pool", "type": "timeseries"}, {"gridPos": {"h": 1, "w": 24, "x": 0, "y": 29}, "id": 16, "title": "Server Metrics", "type": "row"}, {"datasource": {"type": "prometheus", "uid": "fdwe81suzec5ce"}, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 50, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "smooth", "lineWidth": 1, "pointSize": 2, "scaleDistribution": {"type": "linear"}, "showPoints": "always", "spanNulls": false, "stacking": {"group": "A", "mode": "normal"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 6, "w": 6, "x": 0, "y": 30}, "id": 26, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": false}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "fdwe81suzec5ce"}, "disableTextWrap": false, "editorMode": "builder", "exemplar": false, "expr": "pgcat_servers_idle_count{pool=~\"$pool\", role=~\"$role\", shard=~\"$shard_id\", index=~\"$instance_index\", host=~\"$host\", user=~\"$user\"}", "fullMetaSearch": false, "includeNullMetadata": true, "instant": false, "legendFormat": "host:{{host}}, identifier:{{role}}{{index}}, shard_id:{{shard}}, pool:{{pool}}, user:{{user}}", "range": true, "refId": "A", "useBackend": false}], "title": "Idle Server Connections", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "fdwe81suzec5ce"}, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 50, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "smooth", "lineWidth": 1, "pointSize": 2, "scaleDistribution": {"type": "linear"}, "showPoints": "always", "spanNulls": false, "stacking": {"group": "A", "mode": "normal"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 6, "w": 6, "x": 6, "y": 30}, "id": 27, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": false}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "fdwe81suzec5ce"}, "disableTextWrap": false, "editorMode": "builder", "exemplar": false, "expr": "pgcat_servers_active_count{pool=~\"$pool\", role=~\"$role\", shard=~\"$shard_id\", index=~\"$instance_index\", host=~\"$host\", user=~\"$user\"}", "fullMetaSearch": false, "includeNullMetadata": true, "instant": false, "legendFormat": "host:{{host}}, identifier:{{role}}{{index}}, shard_id:{{shard}}, pool:{{pool}}, user:{{user}}", "range": true, "refId": "A", "useBackend": false}], "title": "Active Server Connections", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "fdwe81suzec5ce"}, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 50, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "smooth", "lineWidth": 1, "pointSize": 2, "scaleDistribution": {"type": "linear"}, "showPoints": "always", "spanNulls": false, "stacking": {"group": "A", "mode": "normal"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 6, "w": 6, "x": 12, "y": 30}, "id": 28, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": false}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "fdwe81suzec5ce"}, "disableTextWrap": false, "editorMode": "builder", "exemplar": false, "expr": "pgcat_servers_login_count{pool=~\"$pool\", role=~\"$role\", shard=~\"$shard_id\", index=~\"$instance_index\", host=~\"$host\", user=~\"$user\"}", "fullMetaSearch": false, "includeNullMetadata": true, "instant": false, "legendFormat": "host:{{host}}, identifier:{{role}}{{index}}, shard_id:{{shard}}, pool:{{pool}}, user:{{user}}", "range": true, "refId": "A", "useBackend": false}], "title": "Server Connection in Login State", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "fdwe81suzec5ce"}, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 50, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "smooth", "lineWidth": 1, "pointSize": 2, "scaleDistribution": {"type": "linear"}, "showPoints": "always", "spanNulls": false, "stacking": {"group": "A", "mode": "normal"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 6, "w": 6, "x": 18, "y": 30}, "id": 29, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": false}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "fdwe81suzec5ce"}, "disableTextWrap": false, "editorMode": "builder", "exemplar": false, "expr": "pgcat_servers_tested_count{pool=~\"$pool\", role=~\"$role\", shard=~\"$shard_id\", index=~\"$instance_index\", host=~\"$host\", user=~\"$user\"}", "fullMetaSearch": false, "includeNullMetadata": true, "instant": false, "legendFormat": "host:{{host}}, identifier:{{role}}{{index}}, shard_id:{{shard}}, pool:{{pool}}, user:{{user}}", "range": true, "refId": "A", "useBackend": false}], "title": "Server Connection in Tested State", "type": "timeseries"}, {"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 36}, "id": 12, "panels": [], "title": "Client Metrics", "type": "row"}, {"datasource": {"type": "prometheus", "uid": "fdwe81suzec5ce"}, "description": "The number of Clients currently connected but not assigned a server connection nor are they seeking one", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 50, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "smooth", "lineStyle": {"fill": "solid"}, "lineWidth": 1, "pointSize": 4, "scaleDistribution": {"type": "linear"}, "showPoints": "always", "spanNulls": false, "stacking": {"group": "A", "mode": "normal"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 6, "w": 8, "x": 0, "y": 37}, "id": 14, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "fdwe81suzec5ce"}, "disableTextWrap": false, "editorMode": "builder", "exemplar": false, "expr": "pgcat_pools_cl_idle{pool=~\"$pool\", user=~\"$user\"}", "fullMetaSearch": false, "includeNullMetadata": true, "instant": false, "legendFormat": "Pool:{{pool}}, User:{{user}}", "range": true, "refId": "A", "useBackend": false}], "title": "Idle Clients", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "fdwe81suzec5ce"}, "description": "The number of Clients currently assigned a server connection", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 50, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "smooth", "lineStyle": {"fill": "solid"}, "lineWidth": 1, "pointSize": 2, "scaleDistribution": {"type": "linear"}, "showPoints": "always", "spanNulls": false, "stacking": {"group": "A", "mode": "normal"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 6, "w": 8, "x": 8, "y": 37}, "id": 1, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "fdwe81suzec5ce"}, "disableTextWrap": false, "editorMode": "builder", "exemplar": false, "expr": "pgcat_pools_cl_active{pool=~\"$pool\", user=~\"$user\"}", "fullMetaSearch": false, "includeNullMetadata": true, "instant": false, "legendFormat": "Pool:{{pool}}, User:{{user}}", "range": true, "refId": "A", "useBackend": false}], "title": "Active Clients", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "fdwe81suzec5ce"}, "description": "The number of Clients currently waiting in the queue waiting for a server connection to be assigned. This should remain close to 0. Any persistent deviation from zero means clients are blocked from querying the database", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 50, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "smooth", "lineStyle": {"fill": "solid"}, "lineWidth": 1, "pointSize": 2, "scaleDistribution": {"type": "linear"}, "showPoints": "always", "spanNulls": false, "stacking": {"group": "A", "mode": "normal"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 6, "w": 8, "x": 16, "y": 37}, "id": 15, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "fdwe81suzec5ce"}, "disableTextWrap": false, "editorMode": "builder", "exemplar": false, "expr": "pgcat_pools_cl_waiting{pool=~\"$pool\", user=~\"$user\"}", "fullMetaSearch": false, "includeNullMetadata": true, "instant": false, "legendFormat": "Pool:{{pool}}, User:{{user}}", "range": true, "refId": "A", "useBackend": false}], "title": "Waiting Clients", "type": "timeseries"}], "refresh": "5s", "schemaVersion": 39, "tags": ["Databases", "PostgreSQL"], "templating": {"list": [{"allValue": ".+", "current": {"selected": true, "text": "All", "value": "$__all"}, "datasource": {"type": "prometheus", "uid": "fdwe81suzec5ce"}, "definition": "label_values(pgcat_databases_current_connections,role)", "description": "Instance Role in the pool", "hide": 0, "includeAll": true, "label": "Role", "multi": false, "name": "role", "options": [], "query": {"qryType": 1, "query": "label_values(pgcat_databases_current_connections,role)", "refId": "PrometheusVariableQueryEditor-VariableQuery"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "type": "query"}, {"allValue": ".+", "current": {"selected": true, "text": ["All"], "value": ["$__all"]}, "datasource": {"type": "prometheus", "uid": "fdwe81suzec5ce"}, "definition": "label_values(pgcat_databases_current_connections,shard)", "description": "", "hide": 0, "includeAll": true, "label": "Shard ID", "multi": true, "name": "shard_id", "options": [], "query": {"qryType": 1, "query": "label_values(pgcat_databases_current_connections,shard)", "refId": "PrometheusVariableQueryEditor-VariableQuery"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "type": "query"}, {"allValue": ".+", "current": {"selected": true, "text": ["All"], "value": ["$__all"]}, "datasource": {"type": "prometheus", "uid": "fdwe81suzec5ce"}, "definition": "label_values(pgcat_databases_current_connections,index)", "description": "The instance index in the role (e.g. a pool with two replicas will have instances with indices 0 and 1", "hide": 0, "includeAll": true, "label": "Instance Index", "multi": true, "name": "instance_index", "options": [], "query": {"qryType": 1, "query": "label_values(pgcat_databases_current_connections,index)", "refId": "PrometheusVariableQueryEditor-VariableQuery"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "type": "query"}, {"allValue": ".+", "current": {"selected": false, "text": "All", "value": "$__all"}, "datasource": {"type": "prometheus", "uid": "fdwe81suzec5ce"}, "definition": "label_values(pgcat_databases_current_connections,pool)", "description": "The PgCat Connection Pool ", "hide": 0, "includeAll": true, "label": "Pool Name", "multi": true, "name": "pool", "options": [], "query": {"qryType": 1, "query": "label_values(pgcat_databases_current_connections,pool)", "refId": "PrometheusVariableQueryEditor-VariableQuery"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "type": "query"}, {"allValue": ".+", "current": {"selected": true, "text": "All", "value": "$__all"}, "datasource": {"type": "prometheus", "uid": "fdwe81suzec5ce"}, "definition": "label_values(pgcat_databases_current_connections,host)", "description": "The underlying Database host name", "hide": 0, "includeAll": true, "label": "Host", "multi": false, "name": "host", "options": [], "query": {"qryType": 1, "query": "label_values(pgcat_databases_current_connections,host)", "refId": "PrometheusVariableQueryEditor-VariableQuery"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "type": "query"}, {"allValue": ".+", "current": {"selected": true, "text": ["All"], "value": ["$__all"]}, "datasource": {"type": "prometheus", "uid": "fdwe81suzec5ce"}, "definition": "label_values(usename)", "description": "PostgreSQL username used by the pool", "hide": 0, "includeAll": true, "label": "Username", "multi": true, "name": "user", "options": [], "query": {"qryType": 1, "query": "label_values(usename)", "refId": "PrometheusVariableQueryEditor-VariableQuery"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "type": "query"}]}, "time": {"from": "now-30m", "to": "now"}, "timepicker": {}, "timezone": "browser", "title": "PgCat Dashboard", "uid": "ddwejyl5j6jnkb", "version": 46, "weekStart": ""}