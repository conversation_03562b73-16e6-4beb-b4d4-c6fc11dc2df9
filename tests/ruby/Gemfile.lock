GEM
  remote: https://rubygems.org/
  specs:
    activemodel (7.1.4)
      activesupport (= 7.1.4)
    activerecord (7.1.4)
      activemodel (= 7.1.4)
      activesupport (= 7.1.4)
      timeout (>= 0.4.0)
    activesupport (7.1.4)
      base64
      bigdecimal
      concurrent-ruby (~> 1.0, >= 1.0.2)
      connection_pool (>= 2.2.5)
      drb
      i18n (>= 1.6, < 2)
      minitest (>= 5.1)
      mutex_m
      tzinfo (~> 2.0)
    ast (2.4.2)
    base64 (0.2.0)
    bigdecimal (3.1.8)
    concurrent-ruby (1.3.4)
    connection_pool (2.4.1)
    diff-lcs (1.5.0)
    drb (2.2.1)
    i18n (1.14.5)
      concurrent-ruby (~> 1.0)
    minitest (5.25.1)
    mutex_m (0.2.0)
    parallel (1.22.1)
    parser (3.1.2.0)
      ast (~> 2.4.1)
    parslet (2.0.0)
    pg (1.3.2)
    rainbow (3.1.1)
    regexp_parser (2.3.1)
    rexml (3.3.6)
      strscan
    rspec (3.11.0)
      rspec-core (~> 3.11.0)
      rspec-expectations (~> 3.11.0)
      rspec-mocks (~> 3.11.0)
    rspec-core (3.11.0)
      rspec-support (~> 3.11.0)
    rspec-expectations (3.11.0)
      diff-lcs (>= 1.2.0, < 2.0)
      rspec-support (~> 3.11.0)
    rspec-mocks (3.11.1)
      diff-lcs (>= 1.2.0, < 2.0)
      rspec-support (~> 3.11.0)
    rspec-support (3.11.0)
    rubocop (1.29.0)
      parallel (~> 1.10)
      parser (>= *******)
      rainbow (>= 2.2.2, < 4.0)
      regexp_parser (>= 1.8, < 3.0)
      rexml (>= 3.2.5, < 4.0)
      rubocop-ast (>= 1.17.0, < 2.0)
      ruby-progressbar (~> 1.7)
      unicode-display_width (>= 1.4.0, < 3.0)
    rubocop-ast (1.17.0)
      parser (>= *******)
    ruby-progressbar (1.11.0)
    strscan (3.1.0)
    timeout (0.4.1)
    toml (0.3.0)
      parslet (>= 1.8.0, < 3.0.0)
    toxiproxy (2.0.1)
    tzinfo (2.0.6)
      concurrent-ruby (~> 1.0)
    unicode-display_width (2.1.0)

PLATFORMS
  aarch64-linux
  arm64-darwin-21
  x86_64-linux

DEPENDENCIES
  activerecord
  pg
  rspec
  rubocop
  toml
  toxiproxy

BUNDLED WITH
   2.3.21
