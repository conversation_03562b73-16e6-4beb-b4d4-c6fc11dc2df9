\set ON_ERROR_STOP on

SET SHARDING KEY TO '1';
SELECT * FROM data WHERE id = 1;

SET SHARDING KEY TO '2';
SELECT * FROM data WHERE id = 2;

SET SHARDING KEY TO '3';
SELECT * FROM data WHERE id = 3;

SET SHARDING KEY TO '4';
SELECT * FROM data WHERE id = 4;

SET SHARDING KEY TO '5';
SELECT * FROM data WHERE id = 5;

SET SHARDING KEY TO '6';
SELECT * FROM data WHERE id = 6;

SET SHARDING KEY TO '7';
SELECT * FROM data WHERE id = 7;

SET SHARDING KEY TO '8';
SELECT * FROM data WHERE id = 8;

SET SHARDING KEY TO '9';
SELECT * FROM data WHERE id = 9;

SET SHARDING KEY TO '10';
SELECT * FROM data WHERE id = 10;

SET SHARDING KEY TO '11';
SELECT * FROM data WHERE id = 11;

SET SHARDING KEY TO '12';
SELECT * FROM data WHERE id = 12;

SET SHARDING KEY TO '13';
SELECT * FROM data WHERE id = 13;

SET SHARDING KEY TO '14';
SELECT * FROM data WHERE id = 14;

SET SHARDING KEY TO '15';
SELECT * FROM data WHERE id = 15;

SET SHARDING KEY TO '16';
SELECT * FROM data WHERE id = 16;
