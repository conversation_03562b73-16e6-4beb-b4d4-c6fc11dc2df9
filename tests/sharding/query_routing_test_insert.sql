\set ON_ERROR_STOP on

SET SHARDING KEY TO '1';
INSERT INTO data (id, value) VALUES (1, 'value_1');

SET SHARDING KEY TO '2';
INSERT INTO data (id, value) VALUES (2, 'value_1');

SET SHARDING KEY TO '3';
INSERT INTO data (id, value) VALUES (3, 'value_1');

SET SHARDING KEY TO '4';
INSERT INTO data (id, value) VALUES (4, 'value_1');

SET SHARDING KEY TO '5';
INSERT INTO data (id, value) VALUES (5, 'value_1');

SET SHARDING KEY TO '6';
INSERT INTO data (id, value) VALUES (6, 'value_1');

SET SHARDING KEY TO '7';
INSERT INTO data (id, value) VALUES (7, 'value_1');

SET SHARDING KEY TO '8';
INSERT INTO data (id, value) VALUES (8, 'value_1');

SET SHARDING KEY TO '9';
INSERT INTO data (id, value) VALUES (9, 'value_1');

SET SHARDING KEY TO '10';
INSERT INTO data (id, value) VALUES (10, 'value_1');

SET SHARDING KEY TO '11';
INSERT INTO data (id, value) VALUES (11, 'value_1');

SET SHARDING KEY TO '12';
INSERT INTO data (id, value) VALUES (12, 'value_1');

SET SHARDING KEY TO '13';
INSERT INTO data (id, value) VALUES (13, 'value_1');

SET SHARDING KEY TO '14';
INSERT INTO data (id, value) VALUES (14, 'value_1');

SET SHARDING KEY TO '15';
INSERT INTO data (id, value) VALUES (15, 'value_1');

SET SHARDING KEY TO '16';
INSERT INTO data (id, value) VALUES (16, 'value_1');

set sharding key to '17';
INSERT INTO data (id, value) VALUES (17, 'value_1');

SeT SHaRDInG KeY to '18';
INSERT INTO data (id, value) VALUES (18, 'value_1');
