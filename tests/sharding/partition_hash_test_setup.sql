DROP TABLE IF EXISTS shards CASCADE;

CREATE TABLE shards (
    id BIGINT,
    value VARCHAR
) PARTITION BY HASH (id);

-- DROP TABLE IF EXISTS shard_0;
CREATE TABLE shard_0 PARTITION OF shards FOR VALUES WITH (MODULUS 5, REMAINDER 0);
-- DROP TABLE IF EXISTS shard_1;
CREATE TABLE shard_1 PARTITION OF shards FOR VALUES WITH (MODULUS 5, REMAINDER 1);
-- DROP TABLE IF EXISTS shard_2;
CREATE TABLE shard_2 PARTITION OF shards FOR VALUES WITH (MODULUS 5, REMAINDER 2);
-- DROP TABLE IF EXISTS shard_3;
CREATE TABLE shard_3 PARTITION OF shards FOR VALUES WITH (MODULUS 5, REMAINDER 3);
-- DROP TABLE IF EXISTS shard_4;
CREATE TABLE shard_4 PARTITION OF shards FOR VALUES WITH (MODULUS 5, REMAINDER 4);


INSERT INTO shards SELECT generate_series(1, 500), 'value';

SELECT * FROM shard_0 ORDER BY id LIMIT 10;
SELECT * FROM shard_1 ORDER BY id LIMIT 10;
SELECT * FROM shard_2 ORDER BY id LIMIT 10;
SELECT * FROM shard_3 ORDER BY id LIMIT 10;
SELECT * FROM shard_4 ORDER BY id LIMIT 10;
