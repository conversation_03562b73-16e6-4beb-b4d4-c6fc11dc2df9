# Use the latest 2.1 version of CircleCI pipeline process engine.
# See: https://circleci.com/docs/2.0/configuration-reference
version: 2.1

# Define a job to be invoked later in a workflow.
# See: https://circleci.com/docs/2.0/configuration-reference/#jobs
jobs:
  build:
    # Specify the execution environment. You can specify an image from Dockerhub or use one of our Convenience Images from CircleCI's Developer Hub.
    # See: https://circleci.com/docs/2.0/configuration-reference/#docker-machine-macos-windows-executor
    docker:
      - image: ghcr.io/postgresml/pgcat-ci:latest
        environment:
          RUST_LOG: info
          LLVM_PROFILE_FILE: /tmp/pgcat-%m-%p.profraw
          RUSTC_BOOTSTRAP: 1
          CARGO_INCREMENTAL: 0
          RUSTFLAGS: "-Zprofile -Ccodegen-units=1 -Copt-level=0 -Clink-dead-code -Coverflow-checks=off -Zpanic_abort_tests -Cpanic=abort -Cinstrument-coverage"
          RUSTDOCFLAGS: "-Cpanic=abort"
      - image: postgres:14
        command: ["postgres", "-p", "5432", "-c", "shared_preload_libraries=pg_stat_statements", "-c", "pg_stat_statements.track=all", "-c", "pg_stat_statements.max=100000"]
        environment:
          POSTGRES_USER: postgres
          POSTGRES_DB: postgres
          POSTGRES_PASSWORD: postgres
          POSTGRES_INITDB_ARGS: --auth-local=md5 --auth-host=md5 --auth=md5
      - image: postgres:14
        command: ["postgres", "-p", "7432", "-c", "shared_preload_libraries=pg_stat_statements", "-c", "pg_stat_statements.track=all", "-c", "pg_stat_statements.max=100000"]
        environment:
          POSTGRES_USER: postgres
          POSTGRES_DB: postgres
          POSTGRES_PASSWORD: postgres
          POSTGRES_INITDB_ARGS: --auth-local=scram-sha-256 --auth-host=scram-sha-256 --auth=scram-sha-256
      - image: postgres:14
        command: ["postgres", "-p", "8432", "-c", "shared_preload_libraries=pg_stat_statements", "-c", "pg_stat_statements.track=all", "-c", "pg_stat_statements.max=100000"]
        environment:
          POSTGRES_USER: postgres
          POSTGRES_DB: postgres
          POSTGRES_PASSWORD: postgres
          POSTGRES_INITDB_ARGS: --auth-local=scram-sha-256 --auth-host=scram-sha-256 --auth=scram-sha-256
      - image: postgres:14
        command: ["postgres", "-p", "9432", "-c", "shared_preload_libraries=pg_stat_statements", "-c", "pg_stat_statements.track=all", "-c", "pg_stat_statements.max=100000"]
        environment:
          POSTGRES_USER: postgres
          POSTGRES_DB: postgres
          POSTGRES_PASSWORD: postgres
          POSTGRES_INITDB_ARGS: --auth-local=scram-sha-256 --auth-host=scram-sha-256 --auth=scram-sha-256

      - image: postgres:14
        command: ["postgres", "-p", "10432", "-c", "shared_preload_libraries=pg_stat_statements"]
        environment:
          POSTGRES_USER: postgres
          POSTGRES_DB: postgres
          POSTGRES_PASSWORD: postgres
          POSTGRES_INITDB_ARGS: --auth-local=md5 --auth-host=md5 --auth=md5

    # Add steps to the job
    # See: https://circleci.com/docs/2.0/configuration-reference/#steps
    steps:
      - checkout
      - restore_cache:
          key: cargo-lock-2-{{ checksum "Cargo.lock" }}
      - run:
          name: "Lint"
          command: "cargo fmt --check"
      - run:
          name: "Clippy"
          command: "cargo clippy --all --all-targets -- -Dwarnings"
      - run:
          name: "Tests"
          command: "cargo clean && cargo build && cargo test && bash .circleci/run_tests.sh && .circleci/generate_coverage.sh"
      - store_artifacts:
          path: /tmp/cov
          destination: coverage-data
      - save_cache:
          key: cargo-lock-2-{{ checksum "Cargo.lock" }}
          paths:
            - target
            - ~/.cargo


# Invoke jobs via workflows
# See: https://circleci.com/docs/2.0/configuration-reference/#workflows
workflows:
  build:
    jobs:
      - build
