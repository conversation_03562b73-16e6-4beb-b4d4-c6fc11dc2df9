# This is an example of the most basic config
# that will mimic what PgBouncer does in transaction mode with one server.

[general]

host = "0.0.0.0"
port = 6433
admin_username = "pgcat"
admin_password = "pgcat"

[pools.pgml.users.0]
username = "postgres"
password = "postgres"
pool_size = 10
min_pool_size = 1
pool_mode = "transaction"

[pools.pgml.shards.0]
servers = [
  ["127.0.0.1", 28815, "primary"]
]
database = "postgres"
