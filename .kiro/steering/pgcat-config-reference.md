# PgCat 配置参考

## 配置文件结构

PgCat 使用 TOML 格式的配置文件，主要包含以下几个部分：

```toml
[general]
# 通用配置

[pools.pool_name]
# 连接池配置

[pools.pool_name.users.username]
# 用户配置

[[pools.pool_name.shards]]
# 分片配置

[plugins]
# 插件配置
```

## 通用配置 [general]

### 网络配置
```toml
[general]
# 监听地址，默认 "0.0.0.0"
host = "0.0.0.0"

# 监听端口，默认 5432
port = 5432

# 工作线程数，默认 4
worker_threads = 4
```

### 超时配置
```toml
[general]
# 连接超时（毫秒），默认 1000
connect_timeout = 1000

# 空闲超时（毫秒），默认 600000 (10分钟)
idle_timeout = 600000

# 服务器连接生命周期（毫秒），默认 3600000 (1小时)
server_lifetime = 3600000

# 优雅关闭超时（毫秒），默认 60000
shutdown_timeout = 60000

# 健康检查超时（毫秒），默认 1000
healthcheck_timeout = 1000

# 健康检查延迟（毫秒），默认 30000
healthcheck_delay = 30000

# 事务中空闲客户端超时（毫秒），默认 0（禁用）
idle_client_in_transaction_timeout = 0
```

### TCP 配置
```toml
[general]
# TCP keepalive 空闲时间（秒），默认 5
tcp_keepalives_idle = 5

# TCP keepalive 重试次数，默认 5
tcp_keepalives_count = 5

# TCP keepalive 重试间隔（秒），默认 5
tcp_keepalives_interval = 5

# TCP 用户超时（毫秒），默认 10000
tcp_user_timeout = 10000
```

### 日志配置
```toml
[general]
# 记录客户端连接，默认 false
log_client_connections = false

# 记录客户端断开连接，默认 false
log_client_disconnections = false
```

### DNS 缓存配置
```toml
[general]
# 启用 DNS 缓存，默认 false
dns_cache_enabled = false

# DNS 缓存最大 TTL（秒），默认 30
dns_max_ttl = 30
```

### TLS 配置
```toml
[general]
# TLS 证书文件路径
tls_certificate = "/path/to/cert.pem"

# TLS 私钥文件路径
tls_private_key = "/path/to/key.pem"

# 服务器端 TLS，默认 false
server_tls = false

# 验证服务器证书，默认 false
verify_server_certificate = false
```

### 管理员配置
```toml
[general]
# 管理员用户名，默认 "admin"
admin_username = "admin"

# 管理员密码，默认 "admin"
admin_password = "admin"

# 管理员认证类型，默认 "md5"
admin_auth_type = "md5"
```

### Prometheus 配置
```toml
[general]
# 启用 Prometheus 导出器，默认 false
enable_prometheus_exporter = true

# Prometheus 导出器端口，默认 9930
prometheus_exporter_port = 9930
```

### 其他配置
```toml
[general]
# 配置验证，默认 true
validate_config = true

# 配置自动重载间隔（毫秒），默认 None（禁用）
autoreload = 30000

# 服务器轮询，默认 true
server_round_robin = true

# 服务器禁用时间（秒），默认 60
ban_time = 60
```

## 连接池配置 [pools.pool_name]

### 基本配置
```toml
[pools.my_database]
# 池模式：transaction 或 session，默认 "transaction"
pool_mode = "transaction"

# 负载均衡模式：random 或 least_outstanding_connections，默认 "random"
load_balancing_mode = "random"

# 默认服务器角色：primary、replica 或 any，默认 "any"
default_role = "any"
```

### 查询解析配置
```toml
[pools.my_database]
# 启用查询解析器，默认 false
query_parser_enabled = true

# 查询解析器最大长度，默认 None
query_parser_max_length = 1000000

# 启用读写分离，默认 false
query_parser_read_write_splitting = true

# 启用主库读取，默认 false
primary_reads_enabled = true
```

### 分片配置
```toml
[pools.my_database]
# 分片函数：pg_bigint_hash 或 sha1，默认 "pg_bigint_hash"
sharding_function = "pg_bigint_hash"

# 自动分片键
automatic_sharding_key = "table.column"

# 分片键正则表达式
sharding_key_regex = "shard_key:\\s*(\\d+)"

# 分片 ID 正则表达式
shard_id_regex = "shard_id:\\s*(\\d+)"

# 正则搜索限制，默认 1000
regex_search_limit = 1000

# 默认分片：shard_0、random 或 random_healthy，默认 "shard_0"
default_shard = "shard_0"
```

### 数据库活动路由
```toml
[pools.my_database]
# 启用基于数据库活动的路由，默认 false
db_activity_based_routing = true

# 数据库活动初始化延迟（毫秒），默认 100
db_activity_init_delay = 100

# 数据库活动 TTL（秒），默认 900
db_activity_ttl = 900

# 表变更缓存 TTL（毫秒），默认 50
table_mutation_cache_ms_ttl = 50
```

### 认证查询配置
```toml
[pools.my_database]
# 认证查询 SQL
auth_query = "SELECT username, password FROM users WHERE username = $1"

# 认证查询用户
auth_query_user = "auth_user"

# 认证查询密码
auth_query_password = "auth_password"
```

### 其他配置
```toml
[pools.my_database]
# 清理服务器连接，默认 true
cleanup_server_connections = true

# 记录客户端参数状态变更，默认 false
log_client_parameter_status_changes = false

# 预处理语句缓存大小，默认 0（禁用）
prepared_statements_cache_size = 1000

# 检出失败限制
checkout_failure_limit = 10
```

## 用户配置 [pools.pool_name.users.username]

```toml
[pools.my_database.users.my_user]
# 用户密码
password = "password123"

# 认证类型：md5 或 trust，默认 "md5"
auth_type = "md5"

# 服务器用户名（如果不同）
server_username = "db_user"

# 服务器密码（如果不同）
server_password = "db_password"

# 连接池大小，默认 15
pool_size = 25

# 最小连接池大小
min_pool_size = 5

# 池模式覆盖
pool_mode = "session"

# 语句超时（毫秒），默认 0
statement_timeout = 30000

# 连接超时覆盖
connect_timeout = 2000

# 空闲超时覆盖
idle_timeout = 300000

# 服务器生命周期覆盖
server_lifetime = 1800000
```

## 分片配置 [[pools.pool_name.shards]]

```toml
[[pools.my_database.shards]]
# 数据库名称
database = "shard_0"

# 服务器列表
[[pools.my_database.shards.servers]]
host = "localhost"
port = 5432
role = "primary"

[[pools.my_database.shards.servers]]
host = "localhost"
port = 5433
role = "replica"

# 镜像配置（可选）
[[pools.my_database.shards.mirrors]]
host = "mirror-host"
port = 5432
mirroring_target_index = 0
```

## 插件配置 [plugins] 或 [pools.pool_name.plugins]

### 拦截插件
```toml
[plugins.intercept]
enabled = true

[plugins.intercept.queries.query1]
query = "select version()"
schema = [
    ["version", "text"]
]
result = [
    ["PostgreSQL 13.0 (PgCat)"]
]
```

### 查询日志插件
```toml
[plugins.query_logger]
enabled = true
```

### 表访问控制插件
```toml
[plugins.table_access]
enabled = true
tables = ["sensitive_table", "system_catalog"]
```

### 预热插件
```toml
[plugins.prewarmer]
enabled = true
queries = [
    "SET application_name = 'pgcat'",
    "SELECT 1"
]
```

## 配置示例

### 简单配置
```toml
[general]
host = "0.0.0.0"
port = 6432

[pools.postgres]
pool_mode = "transaction"
default_role = "primary"

[pools.postgres.users.postgres]
password = "password"
pool_size = 25

[[pools.postgres.shards]]
database = "postgres"

[[pools.postgres.shards.servers]]
host = "localhost"
port = 5432
role = "primary"
```

### 分片配置
```toml
[general]
host = "0.0.0.0"
port = 6432

[pools.sharded_db]
pool_mode = "transaction"
sharding_function = "pg_bigint_hash"
automatic_sharding_key = "users.id"
query_parser_enabled = true

[pools.sharded_db.users.app_user]
password = "app_password"
pool_size = 20

# 分片 0
[[pools.sharded_db.shards]]
database = "shard_0"

[[pools.sharded_db.shards.servers]]
host = "shard0-primary"
port = 5432
role = "primary"

[[pools.sharded_db.shards.servers]]
host = "shard0-replica"
port = 5432
role = "replica"

# 分片 1
[[pools.sharded_db.shards]]
database = "shard_1"

[[pools.sharded_db.shards.servers]]
host = "shard1-primary"
port = 5432
role = "primary"
```

### 高可用配置
```toml
[general]
host = "0.0.0.0"
port = 6432
healthcheck_timeout = 5000
healthcheck_delay = 10000
ban_time = 30

[pools.ha_db]
pool_mode = "transaction"
load_balancing_mode = "least_outstanding_connections"
query_parser_enabled = true
query_parser_read_write_splitting = true
primary_reads_enabled = false

[pools.ha_db.users.app_user]
password = "secure_password"
pool_size = 50
min_pool_size = 10

[[pools.ha_db.shards]]
database = "main_db"

[[pools.ha_db.shards.servers]]
host = "primary-db"
port = 5432
role = "primary"

[[pools.ha_db.shards.servers]]
host = "replica1-db"
port = 5432
role = "replica"

[[pools.ha_db.shards.servers]]
host = "replica2-db"
port = 5432
role = "replica"
```

这个配置参考提供了 PgCat 所有主要配置选项的详细说明和示例。