# PgCat 开发指导

## 开发环境设置

### 必需工具
- Rust 1.70+ 
- PostgreSQL 服务器（用于测试）
- Docker（可选，用于测试环境）

### 项目结构理解
```
src/
├── main.rs              # 应用入口点
├── lib.rs               # 库模块声明
├── config.rs            # 配置管理
├── client.rs            # 客户端连接处理
├── server.rs            # 服务器连接管理
├── pool.rs              # 连接池
├── query_router.rs      # 查询路由
├── admin.rs             # 管理接口
├── stats.rs             # 统计系统
├── errors.rs            # 错误定义
├── messages.rs          # 协议消息
├── plugins/             # 插件系统
└── ...                  # 其他支持模块
```

## 常见开发任务

### 1. 添加新的配置选项

#### 步骤：
1. 在 `config.rs` 中的相应结构体添加字段
2. 实现默认值函数
3. 添加配置验证逻辑
4. 更新配置文档

#### 示例：
```rust
// 在 General 结构体中添加新字段
pub struct General {
    // ... 现有字段
    #[serde(default = "General::default_new_option")]
    pub new_option: u64,
}

impl General {
    pub fn default_new_option() -> u64 {
        1000
    }
}
```

### 2. 实现新的插件

#### 步骤：
1. 在 `src/plugins/` 创建新文件
2. 实现 `Plugin` trait
3. 在 `plugins/mod.rs` 中导出
4. 在配置中添加插件配置

#### 示例：
```rust
use async_trait::async_trait;
use crate::plugins::{Plugin, PluginOutput};

pub struct MyPlugin {
    pub enabled: bool,
    // 插件特定配置
}

#[async_trait]
impl Plugin for MyPlugin {
    async fn run(
        &mut self,
        query_router: &QueryRouter,
        ast: &Vec<Statement>,
    ) -> Result<PluginOutput, Error> {
        if !self.enabled {
            return Ok(PluginOutput::Allow);
        }
        
        // 插件逻辑
        Ok(PluginOutput::Allow)
    }
}
```

### 3. 添加新的管理命令

#### 步骤：
1. 在 `admin.rs` 的 `handle_admin` 函数中添加新的匹配分支
2. 实现命令处理函数
3. 添加相应的数据获取逻辑

#### 示例：
```rust
match query_parts.first().unwrap_or(&"").to_ascii_uppercase().as_str() {
    // ... 现有命令
    "MYNEWCOMMAND" => {
        trace!("MYNEWCOMMAND");
        handle_my_new_command(stream, query_parts).await
    }
}

async fn handle_my_new_command<T>(
    stream: &mut T,
    _tokens: Vec<&str>,
) -> Result<(), Error>
where
    T: tokio::io::AsyncWrite + std::marker::Unpin,
{
    // 实现命令逻辑
    custom_protocol_response_ok(stream, "MYNEWCOMMAND").await
}
```

### 4. 扩展统计信息

#### 步骤：
1. 在相应的统计结构体中添加新字段
2. 更新统计收集逻辑
3. 在管理接口中显示新统计
4. 添加到 Prometheus 指标（如需要）

#### 示例：
```rust
// 在 AddressStats 中添加新统计
pub struct AddressStats {
    // ... 现有字段
    pub new_metric: Arc<AtomicU64>,
}

// 更新统计
address.stats.new_metric.fetch_add(1, Ordering::Relaxed);
```

## 测试指南

### 单元测试
```bash
cargo test
```

### 集成测试
```bash
# 启动测试环境
./start_test_env.sh

# 运行特定测试
cargo test --test integration_test
```

### 性能测试
```bash
# 使用 pgbench 进行性能测试
pgbench -h localhost -p 6432 -U postgres -c 10 -j 2 -T 60
```

## 调试技巧

### 1. 日志级别
```bash
# 设置详细日志
RUST_LOG=debug cargo run -- --config pgcat.toml

# 特定模块日志
RUST_LOG=pgcat::pool=debug cargo run
```

### 2. 管理接口调试
```sql
-- 连接到管理数据库
psql -h localhost -p 6432 -U admin pgcat

-- 查看连接状态
SHOW CLIENTS;
SHOW SERVERS;
SHOW POOLS;
SHOW STATS;
```

### 3. 性能分析
```bash
# 使用 perf 进行性能分析
perf record --call-graph=dwarf cargo run --release
perf report
```

## 代码风格和最佳实践

### 1. 错误处理
- 使用 `Result<T, Error>` 进行错误传播
- 提供有意义的错误消息
- 在适当的地方记录错误日志

### 2. 异步编程
- 所有 I/O 操作都应该是异步的
- 使用 `tokio::spawn` 创建并发任务
- 避免阻塞操作

### 3. 内存管理
- 使用 `BytesMut` 进行缓冲区操作
- 避免不必要的内存分配
- 使用 `Arc` 和 `Mutex` 进行共享状态

### 4. 配置管理
- 所有可配置的行为都应该通过配置文件控制
- 提供合理的默认值
- 添加配置验证

## 常见问题和解决方案

### 1. 连接池耗尽
- 检查 `pool_size` 配置
- 监控连接泄漏
- 调整超时设置

### 2. 查询路由错误
- 检查分片键配置
- 验证 SQL 解析逻辑
- 查看查询路由日志

### 3. 性能问题
- 检查工作线程数配置
- 监控连接池利用率
- 分析慢查询日志

### 4. 内存使用过高
- 检查缓存配置
- 监控连接数
- 分析内存分配模式

## 发布流程

### 1. 版本更新
1. 更新 `Cargo.toml` 中的版本号
2. 更新 `CHANGELOG.md`
3. 运行完整测试套件
4. 创建 Git 标签

### 2. 构建发布版本
```bash
# 构建优化版本
cargo build --release

# 运行发布前测试
cargo test --release
```

### 3. 文档更新
1. 更新配置文档
2. 更新 API 文档
3. 更新部署指南

这个开发指导提供了在 PgCat 项目中进行开发的基本框架和最佳实践。