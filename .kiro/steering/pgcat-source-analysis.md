# PgCat 源代码架构分析

## 项目概述

PgCat 是一个用 Rust 编写的 PostgreSQL 连接池和代理服务器，提供连接池、负载均衡、分片、查询路由等功能。

## 核心模块分析

### 1. main.rs - 应用程序入口点
- **功能**: 应用程序的主入口，负责初始化和启动服务
- **关键组件**:
  - 使用 Jemalloc 作为内存分配器（非 MSVC 环境）
  - 配置解析和验证
  - TCP 监听器设置
  - 信号处理（SIGINT, SIGTERM, SIGHUP）
  - 客户端连接处理循环
  - Prometheus 指标导出器（可选）
  - DNS 缓存初始化
  - 连接池初始化
  - 优雅关闭机制

### 2. lib.rs - 库模块声明
- **功能**: 定义所有公共模块和工具函数
- **模块结构**:
  - `admin` - 管理接口
  - `auth_passthrough` - 认证透传
  - `client` - 客户端处理
  - `config` - 配置管理
  - `pool` - 连接池
  - `server` - 服务器连接
  - `query_router` - 查询路由
  - `stats` - 统计信息
  - `errors` - 错误处理
  - 其他支持模块

### 3. config.rs - 配置管理系统
- **功能**: 处理配置文件解析、验证和热重载
- **核心结构**:
  - `Config` - 全局配置结构
  - `General` - 通用配置（主机、端口、超时等）
  - `Pool` - 连接池配置
  - `User` - 用户配置
  - `Address` - 服务器地址配置
  - `Shard` - 分片配置
- **特性**:
  - TOML 配置文件支持
  - 配置验证和默认值
  - 热重载支持
  - TLS 配置
  - 认证配置（MD5, Trust）

### 4. client.rs - 客户端连接处理
- **功能**: 处理来自客户端的连接和请求
- **核心组件**:
  - `Client` 结构体 - 客户端状态管理
  - TLS 连接支持
  - PostgreSQL 协议实现
  - 认证处理（MD5, SASL）
  - 查询取消支持
  - 扩展协议支持（预处理语句）
  - 事务模式 vs 会话模式
- **协议支持**:
  - 启动消息处理
  - 查询消息处理
  - 绑定和执行消息
  - 复制协议

### 5. server.rs - 服务器连接管理
- **功能**: 管理到 PostgreSQL 服务器的连接
- **核心组件**:
  - `Server` 结构体 - 服务器连接状态
  - 连接建立和认证
  - TLS 支持
  - 服务器参数管理
  - 查询执行和结果处理
  - 连接清理和重置
  - 镜像支持
- **认证支持**:
  - MD5 密码认证
  - SASL/SCRAM-SHA-256
  - 信任认证

### 6. pool.rs - 连接池管理
- **功能**: 管理数据库连接池
- **核心组件**:
  - `ConnectionPool` - 连接池主结构
  - `PoolSettings` - 池配置
  - `PreparedStatementCache` - 预处理语句缓存
  - 负载均衡算法
  - 健康检查
  - 服务器禁用/启用
- **特性**:
  - 多分片支持
  - 主从复制支持
  - 连接生命周期管理
  - 故障转移
  - 连接池统计

### 7. query_router.rs - 查询路由系统
- **功能**: 智能查询路由和分片
- **核心组件**:
  - `QueryRouter` - 查询路由器
  - SQL 解析和分析
  - 分片键提取
  - 读写分离
  - 自定义命令处理
- **路由策略**:
  - 基于分片键的路由
  - 读写分离路由
  - 数据库活动感知路由
  - 表变更缓存

### 8. admin.rs - 管理接口
- **功能**: 提供管理和监控接口
- **支持的命令**:
  - `SHOW` 命令（STATS, POOLS, CLIENTS, SERVERS 等）
  - `RELOAD` - 配置重载
  - `PAUSE/RESUME` - 池暂停/恢复
  - `BAN/UNBAN` - 服务器禁用/启用
  - `SHUTDOWN` - 优雅关闭
- **监控功能**:
  - 连接统计
  - 性能指标
  - 配置查看
  - 客户端和服务器状态

### 9. stats.rs - 统计系统
- **功能**: 收集和报告性能统计
- **统计类型**:
  - 客户端统计（连接数、查询数、错误数）
  - 服务器统计（连接状态、响应时间）
  - 地址统计（每个服务器的详细指标）
  - 池统计（利用率、等待时间）
- **特性**:
  - 实时统计收集
  - 平均值计算
  - 统计报告生成

### 10. errors.rs - 错误处理
- **功能**: 定义统一的错误类型和处理
- **错误类型**:
  - 客户端错误
  - 服务器错误
  - 配置错误
  - 网络错误
  - 认证错误
  - 协议错误

## 插件系统

PgCat 提供了一个灵活的插件系统，允许在查询执行前进行拦截、修改或记录。

### 插件架构

#### Plugin Trait
- **异步接口**: 所有插件都实现 `Plugin` trait
- **执行时机**: 在查询发送到服务器之前运行
- **输出类型**: `PluginOutput` 枚举定义插件的行为
  - `Allow` - 允许查询继续执行
  - `Deny(String)` - 拒绝查询并返回错误消息
  - `Overwrite(Vec<Statement>)` - 重写查询语句
  - `Intercept(BytesMut)` - 拦截查询并返回自定义结果

### 内置插件

#### 1. intercept.rs - 查询拦截插件
- **功能**: 拦截特定查询并返回预定义的假结果
- **用途**: 
  - 模拟查询结果
  - 隐藏敏感数据
  - 提供缓存响应
- **配置**:
  - 查询匹配规则
  - 自定义结果集架构
  - 预定义数据行
- **特性**:
  - 支持多种数据类型（text, int4, bool, oid 等）
  - 可配置列架构
  - 支持空值处理

#### 2. query_logger.rs - 查询日志插件
- **功能**: 记录所有执行的查询到日志
- **用途**:
  - 审计查询活动
  - 性能分析
  - 调试和监控
- **特性**:
  - 按用户和数据库分类记录
  - 支持多语句查询
  - 集成到标准日志系统

#### 3. table_access.rs - 表访问控制插件
- **功能**: 控制对特定表的访问权限
- **用途**:
  - 数据安全控制
  - 表级权限管理
  - 防止未授权访问
- **特性**:
  - 基于表名的访问控制
  - 支持模式限定的表名
  - 自定义拒绝消息
- **实现**:
  - 使用 SQL 解析器遍历查询中的表关系
  - 检查表名是否在禁止列表中
  - 返回权限拒绝错误

#### 4. prewarmer.rs - 连接预热插件
- **功能**: 在新连接可用前执行预热查询
- **用途**:
  - 初始化连接状态
  - 设置会话参数
  - 预加载缓存
- **特性**:
  - 可配置预热查询列表
  - 异步执行
  - 错误处理和日志记录

### 插件配置和集成

#### 配置结构
- 插件配置在 `config.rs` 中的 `Plugins` 结构体定义
- 每个插件都有独立的配置选项
- 支持启用/禁用控制

#### 执行流程
1. 查询解析后，在路由前执行插件
2. 按配置顺序依次执行插件
3. 根据插件输出决定后续处理
4. 支持插件链式处理

#### 扩展性
- 插件系统设计为可扩展
- 新插件只需实现 `Plugin` trait
- 支持自定义插件逻辑
- 与查询路由器紧密集成

### 使用场景

#### 安全控制
- 阻止危险操作（DROP TABLE, DELETE 等）
- 限制系统表访问
- 实现细粒度权限控制

#### 监控和审计
- 记录所有查询活动
- 性能监控和分析
- 合规性审计

#### 数据保护
- 敏感数据脱敏
- 查询结果过滤
- 数据访问日志

#### 性能优化
- 连接预热
- 查询缓存
- 结果集拦截

## 支持模块

### 11. messages.rs - PostgreSQL 协议消息处理
- **功能**: 实现 PostgreSQL 协议的消息格式和处理
- **核心组件**:
  - 协议消息构造和解析
  - 数据类型映射（Text, Int4, Numeric 等）
  - 认证消息处理（MD5, SASL）
  - 查询和响应消息
  - 扩展协议支持（Parse, Bind, Execute）
- **消息类型**:
  - `Parse` - 预处理语句解析
  - `Bind` - 参数绑定
  - `DataRow` - 数据行
  - `RowDescription` - 行描述
  - `CommandComplete` - 命令完成
  - `ReadyForQuery` - 准备接收查询
- **工具函数**:
  - Socket 配置（TCP keepalive, nodelay）
  - 消息读写操作
  - 错误响应生成

### 12. sharding.rs - 分片算法实现
- **功能**: 实现数据分片的哈希算法
- **支持的算法**:
  - `PgBigintHash` - PostgreSQL 兼容的 BIGINT 哈希算法
  - `Sha1` - SHA1 哈希算法
- **核心组件**:
  - `Sharder` 结构体 - 分片计算器
  - 哈希函数实现
  - 分片数量管理
- **特性**:
  - 与 PostgreSQL HASH 分区兼容
  - 支持多种哈希算法
  - 确定性分片分配

### 13. prometheus.rs - Prometheus 指标导出
- **功能**: 导出 Prometheus 格式的监控指标
- **指标类型**:
  - Counter（计数器）- 只增不减的指标
  - Gauge（仪表）- 可增可减的指标
- **导出的指标**:
  - 统计指标（查询数、事务数、字节数）
  - 连接池指标（活跃连接、空闲连接、等待时间）
  - 服务器指标（状态、错误数、禁用状态）
  - 数据库指标（连接数、池大小）
- **HTTP 服务器**:
  - `/metrics` 端点
  - 标准 Prometheus 格式输出
  - 标签支持（host, shard, role, pool 等）

### 14. tls.rs - TLS/SSL 支持
- **功能**: 提供 TLS 加密连接支持
- **核心组件**:
  - `Tls` 结构体 - TLS 配置管理
  - 证书和私钥加载
  - TLS 接受器配置
  - 证书验证（可选）
- **特性**:
  - 支持 RSA、EC、PKCS8 私钥格式
  - 可配置证书验证
  - 与 Tokio 异步运行时集成

### 15. dns_cache.rs - DNS 缓存系统
- **功能**: 提供 DNS 解析缓存和自动刷新
- **核心组件**:
  - `CachedResolver` - 缓存解析器
  - `AddrSet` - IP 地址集合
  - 自动刷新循环
  - TTL 管理
- **特性**:
  - 异步 DNS 解析
  - 自动缓存刷新
  - 地址变更检测
  - 可配置 TTL
  - 错误处理和重试

### 其他重要模块
- `scram.rs` - SCRAM 认证实现
- `mirrors.rs` - 镜像功能
- `constants.rs` - 常量定义
- `logger.rs` - 日志系统
- `cmd_args.rs` - 命令行参数解析
- `auth_passthrough.rs` - 认证透传

## 架构特点

### 1. 异步架构
- 基于 Tokio 异步运行时
- 非阻塞 I/O 操作
- 高并发支持

### 2. 内存安全
- Rust 语言的内存安全保证
- 无数据竞争
- 零成本抽象

### 3. 高性能
- 连接池复用
- 智能查询路由
- 负载均衡
- 连接预热

### 4. 可观测性
- 详细的统计信息
- Prometheus 指标
- 管理接口
- 日志记录

### 5. 高可用性
- 故障转移
- 健康检查
- 优雅关闭
- 配置热重载

## 数据流

1. **客户端连接** → `client.rs` → 认证 → 连接池分配
2. **查询处理** → `query_router.rs` → 路由决策 → 服务器选择
3. **服务器通信** → `server.rs` → 查询执行 → 结果返回
4. **统计收集** → `stats.rs` → 指标聚合 → 报告生成
5. **管理操作** → `admin.rs` → 命令处理 → 状态更新

## 技术栈和依赖

### 核心依赖
- **Tokio** - 异步运行时和网络 I/O
- **bb8** - 连接池管理
- **sqlparser** - SQL 解析和 AST 生成
- **serde** - 序列化和反序列化
- **rustls** - TLS/SSL 实现
- **trust-dns-resolver** - DNS 解析
- **hyper** - HTTP 服务器（Prometheus）
- **bytes** - 字节缓冲区操作

### 性能优化技术
- **零拷贝** - 使用 `BytesMut` 避免不必要的内存拷贝
- **连接复用** - 智能连接池管理
- **异步 I/O** - 非阻塞网络操作
- **内存池** - Jemalloc 内存分配器
- **缓存机制** - DNS 缓存、预处理语句缓存

### 可靠性保证
- **内存安全** - Rust 语言的所有权系统
- **错误处理** - 完善的错误类型和处理机制
- **优雅关闭** - 信号处理和连接清理
- **健康检查** - 自动故障检测和恢复
- **配置验证** - 启动时配置完整性检查

## 开发和维护指南

### 代码组织原则
1. **模块化设计** - 每个模块职责单一，接口清晰
2. **异步优先** - 所有 I/O 操作都是异步的
3. **错误传播** - 使用 Result 类型进行错误处理
4. **配置驱动** - 行为通过配置文件控制
5. **可测试性** - 模块间低耦合，便于单元测试

### 扩展点
1. **新的分片算法** - 在 `sharding.rs` 中添加
2. **自定义插件** - 实现 `Plugin` trait
3. **新的认证方式** - 扩展认证处理逻辑
4. **监控指标** - 添加新的 Prometheus 指标
5. **协议扩展** - 支持新的 PostgreSQL 协议特性

### 性能调优建议
1. **连接池大小** - 根据负载调整池大小
2. **工作线程数** - 匹配 CPU 核心数
3. **超时设置** - 合理设置各种超时参数
4. **缓存配置** - 启用 DNS 缓存和预处理语句缓存
5. **网络优化** - 调整 TCP keepalive 参数

这个架构设计使 PgCat 能够高效地处理大量并发连接，提供智能的查询路由，并保持高可用性和可观测性。通过模块化的设计和插件系统，PgCat 具有良好的扩展性和可维护性。